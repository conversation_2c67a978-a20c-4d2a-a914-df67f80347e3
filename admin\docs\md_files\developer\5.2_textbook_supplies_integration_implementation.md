# Textbook and Supplies Integration Implementation

## Overview
This document describes the technical implementation of the textbook and supplies integration feature in the assessment module. This feature allows students to include textbooks and school supplies in their assessment with options for complete sets or selected items.

## Implementation Details

### Configuration

The textbook and supplies data is configured in a dedicated policy file (`admin/config/policy/textbook_supplies.js`):

```javascript
define([], function(){
    var TextbookSupplies = {
        "TEXTBOOK_SUPPLIES": {
            "JN": {
                "textbook": 1950,
                "school_supplies": 230,
                "total": 2180
            },
            "SN": {
                "textbook": 2500,
                "school_supplies": 284,
                "total": 2784
            },
            "KN": {
                "textbook": 3850,
                "school_supplies": 316,
                "total": 4166
            },
            "G1": {
                "textbook": 5075,
                "school_supplies": 446,
                "total": 5521
            },
            "G2": {
                "textbook": 5519,
                "school_supplies": 446,
                "total": 5965
            },
            "G3": {
                "textbook": 7118,
                "school_supplies": 475,
                "total": 7593
            },
            "G4": {
                "textbook": 6658,
                "school_supplies": 475,
                "total": 7133
            },
            "G5": {
                "textbook": 7410,
                "school_supplies": 475,
                "total": 7885
            },
            "G6": {
                "textbook": 7731,
                "school_supplies": 475,
                "total": 8206
            },
            "G7": {
                "textbook": 7437,
                "school_supplies": 0,
                "total": 7437
            },
            "G8": {
                "textbook": 7498,
                "school_supplies": 0,
                "total": 7498
            },
            "G9": {
                "textbook": 7374,
                "school_supplies": 0,
                "total": 7374
            },
            "GX": {
                "textbook": 7663,
                "school_supplies": 0,
                "total": 7663
            },
            "GY": {
                "textbook_ABM": 4517,
                "textbook_STEM": 4530,
                "school_supplies": 0,
                "total_ABM": 4517,
                "total_STEM": 4530
            },
            "GZ": {
                "textbook_ABM": 4707,
                "textbook_STEM": 5256,
                "school_supplies": 0,
                "total_ABM": 4707,
                "total_STEM": 5256
            }
        }
    };
    return TextbookSupplies;
});
```

### UI Implementation

The textbook and supplies options are implemented in the assessment view (`admin/views/students/assess.php`):

```html
<a-row>
    <a-col size="6">
        <m-formgroup ng-model="ASC.Assessment.purchase_textbooks_supplies"
            label="Purchase textbooks and school supplies?"
            type="'yesno'"
            help-text="'Select whether you want to include textbooks and school supplies in your assessment'">
        </m-formgroup>
    </a-col>
    <a-col size="6" >
        <m-formgroup ng-model="ASC.Assessment.textbooks_supplies_option"
            label="Purchase Option"
            options="[
                {id:'COMPLETE', name:'Complete set'},
                {id:'SELECTED', name:'Selected items'}
            ]"
            select-prefix="Select Option"
            ng-disabled="ASC.Assessment.purchase_textbooks_supplies === 'N'">
        </m-formgroup>
    </a-col>
</a-row>
```

For the assessment request form, the options are defined in the assessment controller:

```javascript
$scope.TextbookOptions = [
    {id:'COMPLETE', name:'YES - Complete Set'},
    {id:'SELECTED', name:'YES - Selected Items'},
    {id:'NONE', name:'NO'}
];
```

### Controller Logic

The assessment controller (`admin/controllers/students/assessments_controller.js`) includes the following functionality for textbook and supplies:

1. **Option Handling**: The controller watches for changes in the textbook and supplies option:

```javascript
// Watch for changes in textbooks_supplies_option
// When switching from COMPLETE to SELECTED, initialize custom amounts with default values
$selfScope.$watch('ASC.Assessment.textbooks_supplies_option', function(newOption, oldOption) {
    if (newOption === 'COMPLETE' && $scope.Assessment.summary_details) {
        // Initialize custom amounts with the current default values
        $scope.Assessment.custom_textbook_amount = $scope.Assessment.summary_details.textbook_amount;
        $scope.Assessment.custom_supplies_amount = $scope.Assessment.summary_details.supplies_amount;
    }
});
```

2. **Processing Assessment Requests**: When processing an assessment request, the controller maps the textbook option values:

```javascript
switch(AObj.textbook_option){
    case 'COMPLETE': case 'SELECTED':
        $scope.Assessment.purchase_textbooks_supplies = 'Y';
        $scope.Assessment.textbooks_supplies_option = AObj.textbook_option;
    break;
    case 'NONE':
        $scope.Assessment.purchase_textbooks_supplies = 'N';
        $scope.Assessment.textbooks_supplies_option = null;
    break;
}
```

### Data Structure

The textbook and supplies data is stored in the assessment's `summary_details` object:

```javascript
$scope.Assessment.summary_details = {
    // Other assessment details...

    // Textbook and supplies details
    textbook_amount: textbookAmount,
    supplies_amount: suppliesAmount,
    textbooks_supplies_total: textbookSuppliesTotal,

    // Other assessment details...
};
```

## Technical Implementation Notes

### Senior High School Track-Specific Textbooks

For Senior High School (GY and GZ), the system supports track-specific textbook sets:

1. The policy file includes separate pricing for ABM and STEM tracks
2. The controller uses the student's selected track to determine which textbook set to apply
3. The UI displays the appropriate track-specific textbook information

### Custom Amounts for Selected Items

When a user selects "YES - Selected Items":

1. The custom amount fields are enabled
2. The user can enter specific amounts for textbooks and supplies
3. These custom amounts are used in the assessment calculation instead of the default amounts

### Integration with Assessment Calculation

The textbook and supplies amounts are included in the assessment calculation:

1. The total assessment amount includes the textbook and supplies total
2. The payment schedule includes the textbook and supplies amount in the first payment
3. The assessment confirmation modal displays the textbook and supplies details

## Implementation Date

April 2025

## Pricing Updates

**May 19, 2025**: Updated textbook and school supplies pricing across all grade levels to reflect current rates.
