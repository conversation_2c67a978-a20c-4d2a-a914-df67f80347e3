<?php
class OnlinePaymentsController extends AppController {

	var $name = 'OnlinePayments';
	var $uses = array('OnlinePayment','Assessment', 'Student','Api.Jwt','MasterConfig','YearLevel');
	
	function beforeFilter() {
		parent::beforeFilter();
		$this->Auth->allow( 'generate_link', 'payment_success', 'payment_failed', 'payment_cancel', 'add');
	}

	function index() {
		// Use Containable behavior to control what data is fetched
		$this->OnlinePayment->recursive = 3;
		$this->paginate = array(
			'contain' =>array('Attachment','Student.id','Assessment', 'Assessment.AssessmentRequest'	),
			'order' => array('OnlinePayment.created ASC')
		);
		$YList =  $this->YearLevel->find('list');


		$onlinePayments = $this->paginate();
		foreach($onlinePayments as &$OObj):
			$OObj['OnlinePayment']['attachment'] = !empty($OObj['Attachment']) ? $OObj['Attachment'][0] : null;
			$OObj['OnlinePayment']['student_name'] = !empty($OObj['Assessment']['AssessmentRequest']) ? $OObj['Assessment']['AssessmentRequest']['student_name'] : 'Unknown';

			if(!empty($OObj['Assessment']['AssessmentRequest']) ):
				$YLID = $OObj['Assessment']['AssessmentRequest']['incoming_year_level'];
				$OObj['OnlinePayment']['year_level'] =sprintf('Incoming %s',$YList[$YLID]);
			endif;
			// Remove the pr() debug statement
			// Assessment data won't be automatically included unless specifically requested

		endforeach;

		$this->set(compact('onlinePayments'));
	}

	/**
	 * Test ID generation for payment IDs
	 * This function is used for testing the ID generation algorithm
	 * It generates sample IDs for different months and years
	 * Access via: http://localhost/lapis/api/online_payments/test_ids
	 */
	function test_ids() {
		$this->autoRender = false;

		echo "<h1>Payment ID Generation Test</h1>";
		echo "<p>This page tests the generation of 11-character payment IDs with checksums.</p>";

		echo "<h2>Sample IDs for Different Months</h2>";
		echo "<table border='1' cellpadding='5'>";
		echo "<tr><th>Month</th><th>Generated ID</th><th>Length</th><th>Valid?</th></tr>";

		// Test for each month of the current year
		$year = date('Y');
		for ($month = 1; $month <= 12; $month++) {
			$id = $this->OnlinePayment->generateID('CJY', $year, $month);
			$isValid = $this->OnlinePayment->validateID($id);
			$monthName = date('F', mktime(0, 0, 0, $month, 1, $year));

			echo "<tr>";
			echo "<td>{$monthName}</td>";
			echo "<td>{$id}</td>";
			echo "<td>" . strlen($id) . "</td>";
			echo "<td>" . ($isValid ? 'Yes' : 'No') . "</td>";
			echo "</tr>";
		}

		echo "</table>";

		echo "<h2>Sequential IDs for Current Month</h2>";
		echo "<table border='1' cellpadding='5'>";
		echo "<tr><th>#</th><th>Generated ID</th><th>Length</th><th>Valid?</th></tr>";

		// Generate 10 sequential IDs for the current month
		$currentMonth = date('n');
		$lastId = null;

		for ($i = 1; $i <= 10; $i++) {
			if ($i == 1) {
				$id = $this->OnlinePayment->generateID('CJY', $year, $currentMonth);
			} else {
				// Simulate sequential generation by extracting and incrementing the numeric part
				$baseId = substr($lastId, 0, 10); // Remove checksum
				$numericPart = substr($baseId, 6, 4); // Extract numeric part
				$nextNum = intval($numericPart) + 1;
				$nextId = substr($baseId, 0, 6) . str_pad($nextNum, 4, '0', STR_PAD_LEFT);
				$id = $this->OnlinePayment->applyLuhnChecksum($nextId);
			}

			$lastId = $id;
			$isValid = $this->OnlinePayment->validateID($id);

			echo "<tr>";
			echo "<td>{$i}</td>";
			echo "<td>{$id}</td>";
			echo "<td>" . strlen($id) . "</td>";
			echo "<td>" . ($isValid ? 'Yes' : 'No') . "</td>";
			echo "</tr>";
		}

		echo "</table>";
	}

	function add(){
		if (!empty($this->data)) {
			if($this->isAPIRequest()){

				$payment = array();

				if($this->hasBearerToken()):
					$secret = $this->MasterConfig->findBySysKey('AGIMAT_SECRET')['MasterConfig']['sys_value'];
					$token = $this->Session->read('AGIMAT_TOKEN');
					$isTokenValid = $this->Jwt->verifyJWT($token,$secret);
					if(!$isTokenValid):
						return $this->cakeError('invalidToken',array('message'=>$token));
					endif;
				else:
					return $this->cakeError('missingPaymentToken',array('message'=>'Bearer token is missing.'));
				endif;

				$PYOBJ_KEYS = array('ref_no','amount','payment_method','student_id');
				foreach($PYOBJ_KEYS as $key):
					if(isset($this->data['OnlinePayment'][$key])):
						$value = $this->data['OnlinePayment'][$key];
						$payment[$key] = $value;
					else:
						return $this->cakeError('missingPaymentKey',array('message'=>$key));
					endif;
				endforeach;

				$this->Assessment->recusrive = 0;
				$AOBJ = $this->Assessment->getDetails($payment['ref_no']);
				$sy =  floor($AOBJ['Assessment']['esp']);
				$PYID =  $this->OnlinePayment->generateID('CJY',$sy);

				$payment['id'] = $PYID;
				$payment['payment_status'] = 'PENDNG';
				$payment['transaction_date'] =  date('Y-m-d H:i:s');


				$this->OnlinePayment->create();
				if ($this->OnlinePayment->save($payment)) {
					$this->data['OnlinePayment']['id'] =  $this->OnlinePayment->id;
					$this->Session->setFlash(__('Payment added', true));
					$this->redirect(array('action' => 'index'));
				} else {
					$error = implode(',',$this->OnlinePayment->validationErrors);
					$this->Session->setFlash(__('Payment could not be added. Please, try again.', true));
					return $this->cakeError('invalidPaymentDetails',array('message'=>$error));
				}

			}
		}
	}

	/**
	 * Edit/Update payment details
	 */
	function edit($id = null) {
		if (!$id && empty($this->data)) {
			if ($this->isAPIRequest()) {
				return $this->cakeError('invalidPaymentId', array('message' => 'Payment ID is required'));
			}
			$this->Session->setFlash(__('Invalid Payment', true));
			$this->redirect(array('action' => 'index'));
		}

		if (!empty($this->data)) {
			if ($this->isAPIRequest()) {
				// Handle API request for updating payment
				$paymentData = array();

				// Extract payment ID from data or URL parameter
				$paymentId = !empty($this->data['OnlinePayment']['id']) ? $this->data['OnlinePayment']['id'] : $id;

				if (empty($paymentId)) {
					return $this->cakeError('invalidPaymentId', array('message' => 'Payment ID is required'));
				}

				// Validate that the payment exists
				$existingPayment = $this->OnlinePayment->findById($paymentId);
				if (empty($existingPayment)) {
					return $this->cakeError('paymentNotFound', array('message' => 'Payment not found'));
				}

				// Define allowed fields for update
				$allowedFields = array('amount', 'payment_reference', 'payment_method', 'confirmation_date', 'notes', 'payment_status');

				foreach ($allowedFields as $field) {
					if (isset($this->data['OnlinePayment'][$field])) {
						$paymentData[$field] = $this->data['OnlinePayment'][$field];
					}
				}

				// Set the ID for update
				$paymentData['id'] = $paymentId;

				// If status is being changed to approved, set confirmation date if not provided
				if (isset($paymentData['payment_status']) && $paymentData['payment_status'] == 'APPRVD' && empty($paymentData['confirmation_date'])) {
					$paymentData['confirmation_date'] = date('Y-m-d H:i:s');
				}

				$this->OnlinePayment->id = $paymentId;
				if ($this->OnlinePayment->save($paymentData)) {
					// Return success response
					$updatedPayment = $this->OnlinePayment->findById($paymentId);
					$this->set('payment', $updatedPayment);
					return;
				} else {
					$error = implode(',', $this->OnlinePayment->validationErrors);
					return $this->cakeError('invalidPaymentDetails', array('message' => $error));
				}
			} else {
				// Handle regular form submission (non-API)
				if ($this->OnlinePayment->save($this->data)) {
					$this->Session->setFlash(__('The Payment has been saved', true));
					$this->redirect(array('action' => 'index'));
				} else {
					$this->Session->setFlash(__('The Payment could not be saved. Please, try again.', true));
				}
			}
		}

		if (empty($this->data)) {
			$this->data = $this->OnlinePayment->read(null, $id);
		}
	}

	/**
	 * Generate payment link for PayMaya
	 */
	function generate_link() {
		$this->autoRender = false;
		$response = array('status' => 'error', 'message' => 'Invalid request');

		if ($this->RequestHandler->isPost()) {
			$requestData = $this->__getRequestData();

			if (isset($requestData['assessment_id']) && isset($requestData['amount'])) {
				$assessmentId = $requestData['assessment_id'];
				$amount = floatval($requestData['amount']);

				// Get assessment details
				$assessment = $this->Assessment->find('first', array(
					'conditions' => array('Assessment.id' => $assessmentId),
					'contain' => array('Student')
				));

				if ($assessment) {
					// Extract account details
					$accountDetails = json_decode($assessment['Assessment']['account_details'], true);
					$studentName = isset($accountDetails['student_name']) ? $accountDetails['student_name'] : 'Student';

					// Split name into first and last name
					$nameParts = explode(' ', $studentName);
					$lastName = array_pop($nameParts);
					$firstName = implode(' ', $nameParts);

					// Generate payment reference
					$paymentRef = $this->OnlinePayment->generatePaymentReference();

					// Create payment data for PayMaya
					$paymentData = array(
						"totalAmount" => array(
							"value" => $amount,
							"currency" => "PHP"
						),
						"buyer" => array(
							"firstName" => $firstName,
							"lastName" => $lastName,
							"contact" => array(
								"phone" => "N/A",
								"email" => "N/A"
							)
						),
						"items" => array(
							array(
								"amount" => array('value' => $amount),
								"totalAmount" => array('value' => $amount),
								"name" => "Assessment SY 2025-2026",
								"quantity" => 1
							)
						),
						"requestReferenceNumber" => $paymentRef,
						"redirectUrl" => array(
							"success" => Router::url('/api/online_payments/payment_success', true) . "?ref=" . $paymentRef,
							"failure" => Router::url('/api/online_payments/payment_failed', true) . "?ref=" . $paymentRef,
							"cancel" => Router::url('/api/online_payments/payment_cancel', true) . "?ref=" . $paymentRef
						)
					);

					// Save payment record
					$this->OnlinePayment->create();
					$paymentRecord = array(
						'id' => $this->OnlinePayment->generateID('CJY'), // Will automatically use current school year and month
						'assessment_id' => $assessmentId,
						'student_id' => $assessment['Assessment']['student_id'],
						'amount' => $amount,
						'payment_reference' => $paymentRef,
						'payment_method' => 'PayMaya',
						'payment_status' => 'Pending',
						'transaction_date' => date('Y-m-d H:i:s')
					);

					// Debug info
					if (isset($_GET['debug']) && $_GET['debug'] == 1) {
						echo "<pre>";
						echo "Generated ID: " . $paymentRecord['id'] . " (Length: " . strlen($paymentRecord['id']) . ")\n";
						echo "Expected Length: 11 characters (CJY prefix + year code + month code + 4-digit sequence + checksum)\n";
						echo "Table structure:\n";
						$tableInfo = $this->OnlinePayment->query("DESCRIBE online_payments");
						print_r($tableInfo);
						echo "</pre>";
					}

					// Save with validation disabled to see raw errors
					if (!$this->OnlinePayment->save($paymentRecord, false)) {
						// Log the error
						$errorMsg = "Error saving payment record: " . print_r($this->OnlinePayment->validationErrors, true);
						$errorMsg .= " SQL Error: " . $this->OnlinePayment->getDataSource()->lastError();
						CakeLog::write('payment', $errorMsg);

						if (isset($_GET['debug']) && $_GET['debug'] == 1) {
							echo "<pre>Error: " . $errorMsg . "</pre>";
						}
					}

					// For now, we'll simulate the PayMaya API response
					// In production, this would call the actual PayMaya API
					$checkoutUrl = $this->__generateMayaCheckoutUrl($paymentData);

					$response = array(
						'status' => 'success',
						'message' => 'Payment link generated',
						'checkoutUrl' => $checkoutUrl,
						'paymentReference' => $paymentRef
					);
				} else {
					$response['message'] = 'Assessment not found';
				}
			}
		}

		return $this->__jsonResponse($response);
	}

	/**
	 * Handle payment success callback
	 */
	function payment_success() {
		$this->autoRender = false;
		$refNo = isset($this->params['url']['ref']) ? $this->params['url']['ref'] : null;

		if ($refNo) {
			// Update payment status
			$this->OnlinePayment->updateAll(
				array('OnlinePayment.payment_status' => "'Completed'", 'OnlinePayment.confirmation_date' => "'" . date('Y-m-d H:i:s') . "'"),
				array('OnlinePayment.payment_reference' => $refNo)
			);

			// Redirect to success page
			$this->redirect('/pages/payment_success?ref=' . $refNo);
		} else {
			$this->redirect('/pages/pay_online');
		}
	}

	/**
	 * Handle payment failure callback
	 */
	function payment_failed() {
		$this->autoRender = false;
		$refNo = isset($this->params['url']['ref']) ? $this->params['url']['ref'] : null;

		if ($refNo) {
			// Update payment status
			$this->OnlinePayment->updateAll(
				array('OnlinePayment.payment_status' => "'Failed'"),
				array('OnlinePayment.payment_reference' => $refNo)
			);

			// Redirect to failure page
			$this->redirect('/pages/payment_failed?ref=' . $refNo);
		} else {
			$this->redirect('/pages/pay_online');
		}
	}

	/**
	 * Handle payment cancellation callback
	 */
	function payment_cancel() {
		$this->autoRender = false;
		$refNo = isset($this->params['url']['ref']) ? $this->params['url']['ref'] : null;

		if ($refNo) {
			// Update payment status
			$this->OnlinePayment->updateAll(
				array('OnlinePayment.payment_status' => "'Cancelled'"),
				array('OnlinePayment.payment_reference' => $refNo)
			);

			// Redirect to cancellation page
			$this->redirect('/pages/payment_cancelled?ref=' . $refNo);
		} else {
			$this->redirect('/pages/pay_online');
		}
	}

	/**
	 * Generate Maya checkout URL (simulated for development)
	 */
	private function __generateMayaCheckoutUrl($paymentData) {
		// In production, this would call the actual PayMaya API
		// For development, we'll return a simulated URL
		$baseUrl = 'https://sandbox.maya.ph/checkout/';
		$checkoutId = 'dev-' . uniqid();

		return $baseUrl . $checkoutId;
	}

	/**
	 * Get request data from POST
	 */
	private function __getRequestData() {
		$requestData = array();
		$rawData = file_get_contents('php://input');

		if (!empty($rawData)) {
			$requestData = json_decode($rawData, true);
		}

		if (empty($requestData)) {
			$requestData = $this->data;
		}

		return $requestData;
	}

	/**
	 * Return JSON response
	 */
	private function __jsonResponse($data) {
		header('Content-Type: application/json');
		echo json_encode($data);
		exit;
	}


}
