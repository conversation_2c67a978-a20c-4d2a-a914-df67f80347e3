<?php
App::import('Core','Api.ApiAppError');
class AppError extends ApiAppError {
	public function __construct($controller = null, $method = null) {
		$this->CODES['406'] ='Duplicate Ref No %s, already used.';
		$this->CODES['408'] ='User Creation  invalid. %s.';
		$this->CODES['410'] ='User Validation invalid %s.';
		$this->CODES['411'] ='Invalid token.';
		$this->CODES['412'] ='Email sending failed. %s.';
		$this->CODES['414'] ='Missing Payment token.';
		$this->CODES['416'] ='Missing Payment key : %s.';
		$this->CODES['418'] ='Invalid Payment token.';
		$this->CODES['420'] ='Invalid Payment details. %s';
        parent::__construct($controller, $method);
    }
	function duplicateRefNo($params){
		$code = 406;
		$message = sprintf($this->CODES[$code],$params['ref_no']);
		$this->fetchError($code,$message);
	}

	function invalidCreateUser($params){
		$code = 408;
		$message = sprintf($this->CODES[$code],$params['message']);
		$this->fetchError($code,$message);
	}

	function invalidUserValidation($params){
		$code = 410;
		$message = sprintf($this->CODES[$code],$params['message']);
		$this->fetchError($code,$message);
	}

	function invalidEmailSending($params){
		$code = 412;
		$message = sprintf($this->CODES[$code],$params['message']);
		$this->fetchError($code,$message);
	}

	function invalidToken($params){
		$code = 411;
		$message = sprintf($this->CODES[$code],$params['message']);
		$this->fetchError(411,$message);
	}

	function missingPaymentToken($params){
		$code = 414;
		$message = sprintf($this->CODES[$code],$params['message']);
		$this->fetchError($code,$message);
	}


	function missingPaymentKey($params){
		$code = 416;
		$message = sprintf($this->CODES[$code],$params['message']);
		$this->fetchError($code,$message);
	}

	function invalidPaymentToken($params){
		$code = 418;
		$message = sprintf($this->CODES[$code],$params['message']);
		$this->fetchError($code,$message);
	}

	function invalidPaymentDetails($params){
		$code = 420;
		$message = sprintf($this->CODES[$code],$params['message']);
		$this->fetchError($code,$message);
	}

}
?>